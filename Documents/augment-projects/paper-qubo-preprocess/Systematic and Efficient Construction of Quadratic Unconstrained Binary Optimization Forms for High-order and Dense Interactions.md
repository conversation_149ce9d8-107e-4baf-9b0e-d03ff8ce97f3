# Systematic and Efficient Construction of Quadratic Unconstrained Binary Optimization Forms for High-order and Dense Interactions

**<PERSON><PERSON><PERSON> and <PERSON>,³,⁴,⁵**

<EMAIL>.jp¹
¹Recruit Co. Ltd. Tokyo 100-6640 Japan
²Graduate School of Science and Technology, Keio University, Kanagawa 223-8522 Japan
³Department of Applied Physics and Physico-Informatics, Keio University, Kanagawa 223-8522 Japan
⁴Keio University Sustainable Quantum Artificial Intelligence Center (KSQAIC), Keio University, Tokyo 108-8345 Japan
⁵Human Biology-Microbiome-Quantum Research Center (WPI-Bio2Q), Keio University, Tokyo 108-8345 Japan

## Abstract

Quantum Annealing (QA) can efficiently solve combinatorial optimization problems whose objective functions are represented by Quadratic Unconstrained Binary Optimization (QUBO) formulations. For broader applicability of QA, quadratization methods are used to transform higher-order problems into QUBOs. However, quadratization methods for complex problems involving Machine Learning (ML) remain largely unknown. In these problems, strong nonlinearity and dense interactions prevent conventional methods from being applied. Therefore, we model target functions by the sum of rectified linear unit bases, which not only have the ability of universal approximation, but also have an equivalent quadratic-polynomial representation. In this study, the proof of concept is verified both numerically and analytically. In addition, by combining QA with the proposed quadratization, we design a new black-box optimization scheme, in which ML surrogate regressors are inputted to QA after the quadratization process.

## 1 Introduction

Combinatorial optimization problems have numerous real-world applications, spanning diverse fields including logistics, materials science, and finance. In recent years, the size of these problems has increased with the volume of data traffic, which leads to the difficulty of solving them in a realistic time. To address this situation, quantum computing technology has been actively developed. Especially, Quantum Annealing (QA) [1, 2, 3, 4, 5, 6] is a promising metaheuristic to obtain good solutions to combinatorial optimization problems efficiently. Some combinatorial optimization problems can be described by either the Ising model or Quadratic Unconstrained Binary Optimization (QUBO) formulations. The Ising model and QUBO are equivalent via a linear transformation of their variables.

However, combinatorial optimization problems are generally formulated as High-Order Binary Optimization Problems (HOBO) [7, 8] because numerous real-world phenomena exhibit strong nonlinearities. Thus, methods of quadratization [9, 10] from HOBO to QUBO are widely studied to enable QA to handle such problems. Rosenberg's method [11] is known to be one of the typical quadratization. More specifically, a product of two binary variables $x_i, x_j$ (for $i, j = 1, 2, ..., N$) is replaced with an auxiliary binary variable $y_{ij}$ along with a penalty term $-\lambda(x_i x_j - 2x_i y_{ij} - 2x_j y_{ij} + 3y_{ij})$ for a maximization problem. Here, $\lambda > 0$ and $y_{ij} \in \{0,1\}$ denote the penalty coefficient and the auxiliary variable, respectively. N is the size of the problem. For sufficiently large $\lambda$, the penalty term enforces the binary variable assignments such that the constraint $y_{ij} = x_i x_j$ is satisfied. This quadratization is iterated until all of the higher-order interactions are reduced to a quadratic polynomial. Several studies [9, 12] have attempted to reduce the number of auxiliary variables by arranging the order of this quadratization.

<p align="center">
  <img src="fig1.png" alt="Figure 1: Flowchart of QA-based black-box optimization." width="800">
</p>
<p align="center">
  <strong>Figure 1:</strong> (Color online) Basic concept of QA-based black-box optimization by using ML surrogate regressors. Whereas conventional methods adopt ad-hoc QUBO regressors that can be directly input to QA, the ML regressors are usually higher-order nonlinear functions F(x), which requires quadratization process after training. Originally nonlinear regressors are quadratized to F(x, t) by adding auxiliary variables t. Here, $(x, t)^T$ means transpose of a row vector in which x and t are concatenated, and Q is a QUBO matrix.
</p>

Recently, much attention has been paid to the handling of strong nonlinearities in the field of Machine Learning (ML), such as surrogate regressors for a value function in reinforcement learning [13] and an acquisition function in Bayesian optimization [14]. For these black-box optimizations, good candidates can be predicted by searching for maximum points along the regressors. Finding such points is difficult when the black-box optimization treats a vast exploration space. QA has the potential to accelerate the optimization by efficiently searching for these candidates. However, because QA is specialized for handling QUBOs, the process of black-box optimization should be designed as shown in Fig. 1. In other words, a quadratization step is essential to transfer trained regressors into QUBO forms because ML models are usually higher-order nonlinear. For example, they include non-polynomial basis such as Rectified Linear Unit (ReLU) function [15] and Radial Basis Function (RBF) kernel [16].

On the other hand, related studies on annealing-based black-box optimizations adopt surrogate regressors of a quadratic polynomial [17, 18, 19, 20, 21, 22]. In these methods, the regressors can be directly inputted to QA without the quadratization step. However, as shown before, numerous real-world phenomena exhibit strong nonlinearities. For more practical and broader applicability, ML regressors with higher-order nonlinearity are desired as well as quadratic polynomials. Thus, as shown in Fig. 1, we propose a new black-box optimization method. First, an ML regressor is trained with the available data. Then, the regressor is transferred into a QUBO form with a quadratization method. New candidates are selected with respect to an acquisition function based on the QUBO, by QA. Next, the scores of these candidates x are evaluated. Training data $(x, F_0(x))$ are obtained and the regressor is retrained. Here, $F_0$ means a true but unknown objective function to describe scores. These steps are repeated until the target score is achieved.

The important issue in realizing this optimization is quadratization. Fortunately, in the discrete space $x \in X = \{0, 1\}^N$, many regressors can be reduced to a polynomial after expansion in a series thanks to $x^k = x$ for any natural number $k$. For example, $e^{x_1 + x_2 + x_3} = 1+ (e-1)(x_1 + x_2 + x_3) + (e-1)^2 (x_1x_2 + x_2x_3 + x_3x_1) + (e-1)^3x_1x_2x_3$. However, since the number of monomials grows exponentially with the problem size N, directly encoding them to HOBO is difficult in a large problem. Similarly, encoding ML models is generally challenging. Even if they could be encoded, the subsequent transfer to QUBO becomes intractable with existing quadratization methods involving Rosenberg's method because the quadratization is performed sequentially on each monomial. Therefore, in this study, the efficient quadratization step for such ML regressors is focused.

The remainder of this paper is organized as follows. Section 2 provides a brief summary of related studies. Section 3 proposes our quadratization approach for dense and highly nonlinear functions involving ML regressors. Section 4 describes application to several typical ML regressors. Finally, Section 5 concludes this paper.

## 2 Related Work

In this study, we propose a systematic and efficient QUBO formulation for functions with high-order and dense interactions involving ML regressors. Our target is maximizing
$$
F(x) = \sum_{k=1}^K c_k f(q_k(x)). \quad (1)
$$
Here, we assume that $q_k : x \to \mathbb{R}$ is a scalar map and $f$ is a continuous nonlinear function. As shown later, many ML regressors are modeled by this type of function. As a standard technique for polynomial reduction, Piecewise-Linear Approximations (PLAs) [23] are well-established. However, PLAs for high-dimensional problems handled by ML remain largely unknown. Although ML models are not specifically targeted, several attempts to obtain QUBO formulations for activation functions have been reported. Previous studies [24, 25] showed that ReLU functions
$$
R(q) = \begin{cases} 0 & \text{for } q < 0, \\ x & \text{for } q \ge 0 \end{cases} \quad (2)
$$
can be transformed to
$$
R(q) = \max_{t \in [0,1]} \{tq\}. \quad (3)
$$
$t$ is the dual variable introduced through the Legendre transformation [26], and ReLU functions become quadratic with respect to $q, t$.

## 3 Proposed Method

We propose the following two quadratization methods for ML models. The first is to linearly discretize original functions by one-hot vectorization. The second is to expand the function using ReLU functions.

### 3.1 Discretization method

If $q$ is discrete, $f$ can be rigorously decomposed into $f(q) \to \sum_{l=1}^L f(d_l)s_l$ by PLAs [23]. Here, we consider that $q$ is the $L$-ary variable and $d_l$ is the constant value at the $l$-th level. $L$ represents the total number of levels. The auxiliary variables $s_l$ for $l = 1, ..., L$ are one-hot vectors.
$$
f(q) = \max_{s_l \in \{0,1\}} \left\{ \sum_{l=1}^L f(d_l)s_l - \lambda\left(\sum_{l=1}^L d_l s_l - q\right)^2 - \lambda'\left(\sum_{l=1}^L s_l - 1\right)^2 \right\} \quad (4)
$$
is obtained. Equation (4) is quadratic with respect to $q$ and $s_l$ for $l = 1, ..., L$. We refer to this as the discretization method. In this method, maximizing Eq. (1) is
$$
\max_x F(x) = \max_{x} \max_{s \in \{0,1\}^{KL}} \sum_{k=1}^K \left\{ \sum_{l=1}^L c_k f(d_{k,l})s_{k,l} - \lambda_k\left(\sum_{l=1}^L d_{k,l}s_{k,l} - q_k(x)\right)^2 - \lambda'_k\left(\sum_{l=1}^L s_{k,l} - 1\right)^2 \right\}. \quad (5)
$$
In this case, it is not necessary to use annealing-based solvers. Equation (4) can be regarded as an integer linear programming problem, because the first term is linear, and the second and third terms are originated from linear constraints. In addition, several reports recently showed that such problems can be solved by tensor networks [27, 28].

In the discretization method, the number of auxiliary variables increases linearly with respect to $L$. In addition, adjusting the penalty coefficients $\lambda, \lambda'$ is difficult because ill-adjustments lead to invoking infeasible solutions or shifting optimal solutions. Therefore, we propose an alternative method for QUBO formulation in the following subsections.

### 3.2 ReLU-expansion method

Our idea is that $f$ is approximated by the linear sum of a $q$-space basis that has an equivalent quadratic representation. When a non-polynomial basis is adopted, this approximation is justified by the universal approximation theorem [29], which states that neural networks (NNs) with one hidden layer can approximate continuous functions on compact sets with any desired precision. Specifically, we propose the ReLU-expansion method in which a ReLU-type basis is adopted. Here, $f$ is approximated by a polyline as shown in Fig. 2,
$$
\tilde{f}(q) \approx f(q) = \sum_{m=0}^{M-1} (a_m q + b_m) I_{\alpha_m \le q < \alpha_{m+1}}, \quad (6)
$$
where $I_{\alpha \le q < \beta}$ is the indicator function that takes the value of 1 for $\alpha \le q < \beta$ and 0 otherwise. Let $M$ be the number of pieces and let $\alpha$ increase monotonically, in other words, $\alpha_0 < \alpha_1 < \dots < \alpha_M$. We assume the continuity of the right-hand side in Eq. (6) at $q = \alpha_1, \dots, \alpha_{M-1}$ (i.e., $a_m \alpha_m + b_m = a_{m-1} \alpha_{m-1} + b_{m-1}$ for $m = 2, 3, \dots, M-1$). Equation (6) is rewritten by the sum of ReLU functions (2),
$$
\tilde{f}(q) = a_0 q + b_0 + \sum_{m=1}^M (a_m - a_{m-1}) R(q - \alpha_m). \quad (7)
$$
Here, we let $a_M = 0$. Thus, when the polylines along $f$ are obtained, we can derive the ReLU-expanded approximation through Eq. (7).

<p align="center">
  <img src="fig2.png" alt="Figure 2: Approximation of a function by a polyline." width="500">
</p>
<p align="center">
  <strong>Figure 2:</strong> (Color online) Black line f(q) is approximated by a gray polyline. As an example, f(q) = e⁻q is depicted. The polyline is modeled by connecting the tangent lines (dashed lines).
</p>

A pseudocode for the ReLU-expansion method is presented in Algorithm 1. We propose to numerically determine $a_m, b_m$, and $\alpha_m$, and there are several candidates of the procedures. For example, these parameters can be optimized by minimizing the integration of the residual errors in Eq. (6). Specifically, if $f$ is downward convex, the tangent polyline is always below $f$, as shown in Fig. 2. Thus, the shaded area is maximized for goodness of fitting. For another example, one can use first-Spline interpolation [30] to derive the polylines. Note that the values of parameters can be estimated analytically for specific types of functions as shown in Appendix A.

> **Algorithm 1 ReLU-expansion method**
> 1.  **Input:** $M, D, F(x) = \sum_{k=1}^K c_k f(q_k(x))$
> 2.  **Output:** QUBO objective function
> 3.  Model approximated $f(q)$ by a polyline $\tilde{f}(q)$ (Eq. (6))
> 4.  Define residual error between $f(q)$ and $\tilde{f}(q)$
> 5.  Tune model parameters by minimizing the residual error
> 6.  Rewrite $\tilde{f}(q)$ in ReLU form of Eq. (7) to obtain approximate objective function ($\tilde{F}$)
> 7.  **for** $k = 1, 2, ..., K$ **do**
> 8.  &nbsp;&nbsp;&nbsp;&nbsp;**for** $m = 1, 2, ..., M$ **do**
> 9.  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**if** $c_k(a_m - a_{m-1}) > 0$ **then**
> 10. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Perform Legendre transformation (3) for each ReLU function in Eq. (8) by introducing dual variables $t_{k,m}$
> 11. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**else**
> 12. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;In Eq. (8), each Relu function is transformed by Eq. (13), by introducing binary variables $z_{k,m,j}$ for $j=1, ..., D$
> 13. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**end if**
> 14. &nbsp;&nbsp;&nbsp;&nbsp;**end for**
> 15. **end for**
> 16. Approximate objective function (from step 6) is outputted

Thus, the target function is approximated by
$$
\tilde{F}(x) \approx \sum_{k=1}^K c_k \tilde{f}(q_k(x)) \\ = \sum_{k=1}^K c_k(a_{k0}q_k + b_0) + \sum_{k=1}^K \sum_{m=1}^M c_k(a_m - a_{m-1}) R(q_k - \alpha_m). \quad (8)
$$
Maximizing Eq. (8) is
$$
\max_x \tilde{F}(x) \approx \max_x \left\{ \sum_{k=1}^K c_k a_{k0} q_k(x) + \sum_{m=1}^M c_k(a_m - a_{m-1}) \max_{t_{k,m} \in [0,1]} t_{k,m} (q_k(x) - \alpha_m) \right\}, \quad (9)
$$
by using Eq. (3). Here, the constant term of Eq. (8) is omitted because it does not change the set of optimal solutions.

<p align="center">
  <img src="fig3.png" alt="Figure 3: Approximation of a function with varying pieces." width="800">
</p>

<p align="center">
  <strong>Figure 3:</strong> (Color online) Target function F(x) is approximated by the ReLU-expansion method. Approximated functions are plotted as dashed lines with the total number of pieces M = 2, 3, and 4. $F(x) = \sum_{k=1}^5 c_k \exp(-(x - \mu_k)^2 / 2\sigma_k^2)$ is generated randomly by uniform sampling $c_k \in \{-0.5, -0.4, -0.3, ..., 1.0\}$, $\mu_k \in \{-2.0, -1.9, ..., 2.0\}$ and $\sigma_k \in \{0.5, 0.6, ..., 1.0\}$, respectively. With only four pieces M = 4, strongly nonlinear functions are well approximated.
</p>

| M | m | $a_m$ | $b_m$ | $\alpha_m$ |
|---|---|---|---|---|
| **2** | 0 | -1 | 1 | 0 |
| | 1 | -0.0498 | 0.199 | 0.8428 |
| | 2 | - | - | 4 |
| **3** | 0 | -1 | 1 | 0 |
| | 1 | -0.3265 | 0.6920 | 0.4574 |
| | 2 | -0.0498 | 0.1991 | 1.7809 |
| | 3 | - | - | 4 |
| **4** | 0 | -1 | 1 | 0 |
| | 1 | -0.4950 | 0.8431 | 0.3108 |
| | 2 | -0.1959 | 0.5153 | 1.0961 |
| | 3 | -0.0498 | 0.1991 | 2.1633 |
| | 4 | - | - | 4 |
**Table 1:** Optimized parameters of approximate function for f(q) in Fig. 2.

In the ReLU-expansion method, for $c_k(a_m - a_{m-1}) > 0$, the operator $\max_{t_{k,m}}$ in the integrand can be factored in, which leads to the total maximization operator $\max_{t_{k,m}, x}$. On the other hand, for $c_k(a_m - a_{m-1}) < 0$, the max-min operation $\max_x \min_{t_{k,m}}$ remains due to the negative sign and cannot be handled directly by QA.

To resolve such a max-min problem, an encoding technique with Wolfe duality [25] is well-known,
$$
\min_t g(t) \quad \text{s.t.} \quad h_1(t) \le 0, h_2(t) \le 0 \\
\Leftrightarrow \max_{t} \max_{u,v \ge 0} \{g(t) + uh_1(t) + vh_2(t)\} \\
\text{s.t.} \quad \partial_t g(t) + u \partial_t h_1(t) + v \partial_t h_2(t) = 0. \quad (10)
$$
The minimization operator can be transferred to maximization by introducing new auxiliary variables $u \ge 0, v \ge 0$. From Eq. (7), $g(t) = c_k(a_m - a_{m-1})(q_k - \alpha_m)t$, $h_1(t) = -t$ and $h_2(t) = t - 1$ are obtained. The constraints can be encoded by adding the penalty term $(\partial_t g(t) + u \partial_t h_1(t) + v \partial_t h_2(t))^2$ to the objective function.

However, this encoding requires two continuous variables $u, v$, which must be discretized into binary variables for a QUBO formulation. Therefore, for $c_k(a_m - a_{m-1}) < 0$, we adopt another approach to quadratize ReLU functions. Let $q$ be a variable $q = A(q_{int} - \alpha)$, where $q_{int}$ is an integer variable and $A > 0$ and $\alpha$ are real constants. The following binarization is considered.
$$
q_{int} - \lfloor \alpha \rfloor = 1 - 2^D + \sum_{j=0}^D 2^j z_j \quad (11)
$$
$\lfloor x \rfloor$ denotes the floor function to output the greatest integer less than or equal to x. $D$ is the bit width, which is determined to cover the domain of $q_{int} - \alpha$. In other words, $1 - 2^D \le \min(q_{int} - \lfloor \alpha \rfloor)$ and $\max(q_{int} - \lfloor \alpha \rfloor) \le 2^D$. Thus,
$$
D = \log_2(\min(- \lfloor \alpha \rfloor + \max q_{int}, 1 + \lfloor \alpha \rfloor - \min q_{int})) \quad (12)
$$
is obtained. If $z_D = 0$, the right-hand side of Eq. (11) is always lower than 0. Thus, $q/A = q_{int} - \alpha \le q_{int} - \lfloor \alpha \rfloor \le 0$ is obtained. Otherwise, the right-hand side is higher than 1, which leads to $q_{int} - \alpha > q_{int} - \lfloor \alpha \rfloor - 1 \ge 0$. This ensures that the binary variable $z_D$ works as the sign of $q$. For an extension to the case where $q$ is not an integer variable, refer to Appendix B.

Therefore, ReLU functions (2) are rewritten in the form of
$$
R(q) = z_D q, \quad (13)
$$
under the constraint (11). In this study, for negative coefficients $c_k(a_m - a_{m-1}) < 0$ in Eq. (8), $R(q_k - \alpha_m)$ is transferred by Eq. (13). On the other hand, for $c_k(a_m - a_{m-1}) > 0$, Eq. (3) is utilized.

Let $\Delta_p$ be the index set for $(k, m)$, where $c_k(a_m - a_{m-1}) > 0$. We define the complement set as $\Delta_n$. After substituting Eqs. (3) and (13) into Eq. (8) and rearranging the terms, the following expression is obtained.
$$
\max_x F(x) \approx \max_{x} \max_{t_{k,m} \in [0,1], z_{j,k,m} \in \{0,1\}} \left\{ \sum_{k=1}^K c_k a_{k0} q_k(x) \right. \\
+ \sum_{(k,m) \in \Delta_p} c_k(a_m - a_{m-1}) t_{k,m} (q_k(x) - \alpha_m) \\
+ \sum_{(k,m) \in \Delta_n} \{ c_k(a_m - a_{m-1}) z_{k,m,D} (q_k(x) - \alpha_m) \\
\left. - \lambda_{k,m} \left( q_k(x) - \lfloor \alpha_m \rfloor - 1 + 2^D - \sum_{j=0}^D 2^j z_{k,m,j} \right)^2 \} \right\} \quad (14)
$$
The last term is the penalty fucntion for Eq. (11). Equation (14) is the maximization of the quadratic forms with respect to $t_{k,m}$ and $z_{k,m,j}$ for $k = 1, ..., K, m = 1, ..., M, j = 1, ..., D$. If $q_k$ is a linear map, Eq. (14) becomes quadratic also with respect to $x$.

To achieve a QUBO formulation, $t_{k,m}$ must be discretized into binary variables. Fortunately, when $q \neq 0$, Eq. (3) is reduced to
$$
R(q) = \max_{t \in \{0,1\}} \{tq\} \quad \text{for } q \neq 0. \quad (15)
$$
While the previous studies [24, 25] adopted Eq. (3), Eq. (15) can eliminate the degree of freedom required for QUBO formulations. Thus, $t_{k,m}$ becomes a binary variable when $q_k(x) - \alpha_m$ does not take the value of 0. In this case, $t_{k,m}$ does not require to be discretized into binary variables.

### 3.3 Numerical test for ReLU-expansion method

Because each of Eqs. (3) and (13) is equivalent transformation itself, the exactness of the ReLU-expansion method depends mainly on the residual error in Eq. (7). Thus, for verification, we numerically investigated the fidelity to $F(x)$. A target function is $F(x) = \sum_{k=1}^5 c_k e^{-(x - \mu_k)^2 / 2\sigma_k^2}$ and the parameters $c_k, \mu_k$, and $\sigma_k$ are generated randomly.

Setting $q_k(x) = (x - \mu_k)^2 / 2\sigma_k^2$ and $f(q_k) = e^{-q_k}$, the approximate function $\tilde{f}$ in Eq. (6) is estimated.

Figure 2 shows the approximated target functions for $M \le 4$. The polyline parameters for the initial and final tangent lines $T_{ini}, T_{fin}$ were designed to pass through points (0, 1), and (4, 0), respectively. The parameters for the middle tangent lines were optimized by maximizing the shaded area with sequential least quadratic programming [31], in the following procedure. Parameterizing the coordinates of tangent points by $q_t$, the shaded area was calculated after the middle tangent lines were analytically derived. This area was maximized along $q_t$ to obtain the optimal tangent points, by SciPy SLSQP [32] with default parameters. The optimized values are summarized in Table 1.

The approximate function was obtained by substituting the optimized parameters into Eq. (8). In the case of $M = 2$, the approximate function was obtained by connecting only $T_{ini}$ and $T_{fin}$. Figure 3 shows the target function and the approximate functions. As the total number of pieces $M$ increases, the fidelity is improved.

## 4 Application to ML Regressors

Using the discretization method and the ReLU-expansion method, typical ML models are encoded to QUBO. We compare their costs, such as the number of auxiliary variables and that of penalty terms as shown in Table 2.

**Table 2:** Comparison of the numbers of required auxiliary binary variables and comparison of the numbers of required penalty terms. These values are represented by #A and #P, respectively. N denotes the problem size, in other words the dimension of x. M and K denote the total number of pieces in the ReLU-expansion method and that of clusters of a GMM. $K = K_p + K_n$ is the number of training data in a KR, of which $K_p$ data have positive coefficients and $K_n$ data have negative ones in Eq. (18). $K = K_p + K_n$ is the total number of hidden layer nodes in an NN, of which $K_p$ nodes have positive coefficients and $K_n$ nodes have negative ones in Eq. (19).

| | | Discretization | ReLU exp. | Mixed |
| :--- | :--- | :--- | :--- | :--- |
| **GMM** | #A | (N+1)K | MK | - |
| | #P | 2K | 0 | - |
| **KR** | #A | (N+1)K | MK$_p$ + MK$_n$D | MK$_p$ + (N+1)K$_n$ |
| | #P | 2K | MK$_n$ | 2K$_n$ |
| **NN** | #A | - | K$_p$ + K$_n$D | - |
| | #P | - | K$_n$ | - |

### 4.1 Gaussian mixture model

The first problem is Gaussian Mixture Models (GMMs) [33],
$$
F_{GMM}(x) = \sum_{k=1}^K p_k (\pi / \sigma_k^2)^{-N/2} e^{-|x-\mu_k|^2 / 2\sigma_k^2}. \quad (16)
$$
Here, we consider the discrete space $x \in \{0,1\}^N$. $K$ is the total number of clusters. Then $p_k > 0$ denotes the weight in the k-th cluster and satisfies $\sum_{k=1}^K p_k = 1$. $\sigma_k > 0$ and $\mu_k$ denote the variance parameter and the mean vector in the k-th cluster, respectively. We assume $\mu_k \in \{0,1\}^N$ for $k = 1, ..., K$. Equation (16) is generalized to
$$
F_{GMM}(x) = \sum_{k=1}^K c_k e^{-|x-\mu_k|^2 / 2\sigma_k^2} \quad (17)
$$
where $c_k$ for $k = 1, ..., K$ are positive coefficients. First, the discretization method is applied. The possible values of $q_k(x) = |x - \mu_k|^2 / 2\sigma_k^2$ are $0 / 2\sigma_k^2, 1 / 2\sigma_k^2, ..., N / 2\sigma_k^2$. Therefore, $L = N + 1$ and $d_{k,j} = (j-1) / 2\sigma_k^2$ in Eq. (4). The total number of auxiliary binary variables is estimated by $L \times K = (N + 1)K$ in the discretization method. That of penalty terms is 2K.

The ReLU-expansion method can be applied in the same manner as the example in Fig. 3. Because the coefficients $c_k(a_m - a_{m-1})$ can be assumed to be non-negative according to Appendix A, the introduction of auxiliary variables $z$ by Eq. (11) is not required. Thus, Eq. (9) becomes quadratic with respect to $x, t_{k,m}$ because $q_k(x) = |x - \mu_k|^2 / 2\sigma_k^2 = \sum_{i=1}^N ((1-2\mu_{ki})x_i + \mu_{ki}^2) / 2\sigma_k^2$ is a linear map of $x$. Although $t_{k,m}$ is originally the continuous variable, we can assume $t_{k,m} \in \{0, 1\}$ according to Eq. (15). This is because the value of $\sigma_k^2, \alpha_m$ are generally decimals with large digits, which leads to $q_k(x) - \alpha_m = 0 / 2\sigma_k^2 - \alpha_m, 1/2\sigma_k^2 - \alpha_m, ..., N/2\sigma_k^2 - \alpha_m \neq 0$. Thus, the objective function becomes the QUBO form. The total number of auxiliary binary variables is estimated by $MK$. The total number of penalty terms is 0.

### 4.2 Kernel regressor

The second problem is Kernel Regressors (KRs) [16] and especially an RBF kernel is considered,
$$
F_{KR}(x) = \sum_{k=1}^K c_k e^{-|x-x'_k|^2 / 2\sigma^2}. \quad (18)
$$
Here, we consider the discrete space $x \in \{0,1\}^N$. $K$ is the total number of training data. $\sigma > 0$ and $x'_k$ denote the variance parameter and the vector of $x$ in the k-th training data, respectively. Because KRs have almost the same form as GMMs Eq. (17), the total numbers of auxiliary binary variables and penalty terms in the discretization method are $(N + 1)K$ and $2K$, respectively.

Next, the ReLU-expansion method is applied. The coefficients $c_k(a_m - a_{m-1})$ are not necessarily non-negative, unlike in the case of GMMs. In the following, we consider that the $K_p$ data have positive coefficients and the $K_n$ data have negative coefficients. Obviously, $K = K_p + K_n$ holds. For the negative coefficients, the ReLU-expansion method requires the introduction of $z$ by Eq. (11), leading to the increase of $D$ auxiliary variables and one constraint for each $(k,m) \in \Delta_n$. Thus, for the negative coefficients, the numbers of auxiliary binary variables and penalty terms are $MK_n D$ and $MK_n$, respectively. The total numbers of auxiliary binary variables and penalty terms are $MK_p + MK_n D$ and $MK_n$. Because the bit width $D$ is lower than $\log_2 N$ according to Appendix C, the number of auxiliary binary variables is $MK_p + MK_n \log_2 N$ at most.

With support vector machines, KRs can be more sparse [16]. In other words, the number of coefficients that take the value of 0 in Eq. (18) increases, leading to the effective reduction of $K$. In addition, although the RBF kernel is employed in this experiment, our method can be applied to other kernels as detailed in Appendix D.

### 4.3 Neural network

The third problem is NNs and especially a tri-layer network is considered,
$$
F_{NN}(x) = \sum_{k=1}^K c_k R\left(\sum_{i=1}^N w_{ki} x_i + \theta_k\right) + c_0 \quad (19)
$$
Here, we consider the discrete space $x \in \{0,1\}^N$. $K$ is the dimension of a hidden layer. $w_{ki}$ are the weights and $\theta_k, c_0$ are the biases. These values are obtained through training, to be generally decimals with large digits. Thus, unlike the above examples, $q_k(x) = \sum_{i=1}^N w_{ki} x_i + \theta_k$ is not quantized. The discretization method is difficult to apply because it requires the intractable number of auxiliary variables for $q_k$ that have exponential levels. Therefore, we consider only applying the ReLU-expansion method.

As in the case of GMMs and KRs, the input to ReLU functions is probably not 0. Thus, the auxiliary variables $t_{k,m}$ can be reduced to binary variables. The coefficients $c_k$ are not necessarily non-negative. In the following, we assume that the $K_p$ nodes have positive coefficients and the $K_n$ nodes have negative coefficients in Eq. (19). For the negative coefficients, the ReLU-expansion method requires the introduction of $z$, as explained in Eq. (11). However, the binarization (11) cannot be applied because $q_k(x)$ is generally not decomposed into $A(q_{int} - \alpha)$. Thus, we expand this formulation to non-integer variables by Eq. (22) in Appendix B, leading to the increase of $D$ auxiliary variables and one constraint for each $k \in \Delta_n$. Fortunately, Eq. (19) has already been decomposed into the linear sum of ReLU functions, and there are no residual errors in Eq. (7). Thus, the total numbers of auxiliary binary variables and penalty terms are calculated by substituting $M \to 1, K \to K$ into the results of KRs. In other words, the total numbers of auxiliary binary variables and penalty terms are $K_p + K_n D$ and $K_n$, respectively.

### 4.4 Comparison between Discretization Method and ReLU-expansion Method

As shown in Table 2, the numbers of required auxiliary binary variables and penalty terms are listed. Here, the mixed method means that while the ReLU-expansion method is adopted for the positive coefficients $c_k(a_m - a_{m-1})$, the discretization method is applied to $f(q_k(x))$ that has the negative coefficients.

In GMMs, the ReLU-expansion method is efficient if the total number of pieces $M$ can be suppressed to less than the problem size $N$. In addition, no penalty terms exist. On the other hand, in KRs, the numbers are highly affected by the number of negative coefficients $c_k(a_m - a_{m-1})$. In the optimistic case where $K_n = 0$, the numbers of required auxiliary binary variables and penalty terms are the same as GMMs. However, in the intermediate case where $K_n = K / 2$, the advantage of the ReLU-expansion method disappears when $M > (N + 1) / \log_2 N$. Note that the ReLU-expansion method is the approximate encoding, the accuracy of which depends largely on the value of $M$.

In NNs, the discretization method is principally difficult to be applied and only the ReLU-expansion method is considered. The dependence of the total number of pieces $M$ vanishes because NNs are written naturally in the ReLU form. However, although the QUBO formulation appears to be described with fewer variables than GMMs and KRs, the existence of upper limits of the bit width $D$ is not guaranteed unlike the case of them.

## 5 Conclusion

In this study, we developed new quadratization methods for constructing QUBO forms for high-order and dense interactions. The first is to linearly discretize original functions by one-hot vectorization. The second is to expand the function using ReLU functions. We revealed that these methods can formulate QUBO from typical objective functions of ML regressors, such as GMMs, KRs, and NNs. Then, the numbers of required auxiliary binary variables and penalty terms were estimated, which enlights the guidelines for an efficient QUBO formulation of ML models. As detailed in Appendix D, our method can be applied to other models.

In future work, we will try to tackle actual ML tasks. In other words, we will implement a black-box optimization with ML regressors as shown in Fig. 1, by using our quadratization methods. Because this method covers a strong nonlinearity, we would like to apply it to problems that the conventional methods adopting quadratic polynomial models cannot handle. The proposed method has a wide variety of applications because quadratization is a fundamental preprocess prior to quantum computing technology. For example, it can be applied to quantum approximate optimization algorithm [34, 35], which was partially inspired by digitized QA. Thus, this study will contribute to the development of quantum gate as well as quantum annealing.

**Acknowledgments**

This work was partially supported by the Council for Science, Technology, and Innovation (CSTI) through the Cross-ministerial Strategic Innovation Promotion Program (SIP), “Promoting the application of advanced quantum technology platforms to social issues" (Funding agency: QST), Japan Science and Technology Agency (JST) (Grant Number JPMJPF2221). One of the authors S. T. wishes to express their gratitude to the World Premier International Research Center Initiative (WPI), MEXT, Japan, for their support of the Human Biology-Microbiome-Quantum Research Center (Bio2Q).

---

## Appendix A Explicit ReLU-expansion for $C^2$ functions

Particularly, in the case of $f(q)$ is compact $C^2$, the parameters of Eq. (7) can be deduced analytically. Let $f(q)$ be $C^2$ for $q \in [\alpha_0, \alpha_M]$. By performing integration by parts, $f$ can be rewritten in the following form,
$$
f(q) = f(\alpha_0) + (q - \alpha_0)f'(\alpha_0) + \int_{\alpha_0}^q (q-z)f''(z) dz \quad (20)
$$
Through piecewise quadrature, the third term is transferred into
$$
\int_{\alpha_0}^{\alpha_M} R(q-z)f''(z)dz \approx \lim_{M \to \infty} \delta \sum_{m=1}^M f''(\alpha_0 + \delta m)R(q - \alpha_0 - \delta m), \quad (21)
$$
where $\delta = (\alpha_M - \alpha_0) / M$. This is the infinite sum of ReLU functions. Thus, by adopting $a_m - a_{m-1} = \delta f''(\alpha_0 + \delta m)$ and $\alpha_m = \alpha_0 + \delta m$, Eq. (7) becomes a good approximation which ensures the convergence along Eq. (A). When $f(q)$ is concave downward (i.e., $f''(q) \ge 0$), this $(a_m - a_{m-1})$ is always non-negative, which is consistent with the results in Table 1.

## Appendix B Generalization of Eq. (11) to non-integer variables

In Section 3.2, we propose to utilize Eq. (11) for quadratizing ReLU functions, in the case where $q$ can be decomposed into $A(q_{int} - \alpha)$. We consider the case where this decomposition is not applicable. The binarization is modified to
$$
q \approx (\frac{|max\ q| + |min\ q|}{2D}) (1 - 2^D + \sum_{j=0}^D 2^j z_j). \quad (22)
$$
Similarly to the case where $q_{int}$ is an integer variable, the binary variable $z_D$ works as the sign of $q$. Thus, ReLU functions are also quadratized by Eq. (13). In Eq. (22), we assume the case of interest (i.e., $\min q < 0$ and $\max q > 0$). This is because ReLU functions become linear trivially in the other cases, eliminating the need for quadratization.

## Appendix C Upper bound of bit width for Eq. (11)

For encoding KRs in Section 4.2, the upper bound of the bit width $D$ can be estimated. In the KR model, $q_k = |x - x'_k|^2 / 2\sigma^2$ where integer variable $|x - x'_k|^2$ ranges from 0 to $N$. Thus, Eq. (12) becomes $\log_2(\min(N - \lfloor \alpha \rfloor, 1 + \lfloor \alpha \rfloor))$. Here, the range of interest is $0 < \alpha < N$, for the same reasons as in Appendix B. Thus, $D \le \log_2 N$ is obtained.

## Appendix D Remark on application to other models

Our methods can be applied to other kernels than the RBF kernel. For example, the rational quadratic kernel [36] is downward convex $C^2$ with respect to $|x - x'_k|^2$, in the same manner as the RBF kernel. Thus, the result in Section 4.2 is still valid. In the case of other types of kernels, though our polyline approach can be applied to, the number of required auxiliary binary variables may have different dependencies. This is because $(a_m - a_{m-1})$ is no longer necessarily non-negative.